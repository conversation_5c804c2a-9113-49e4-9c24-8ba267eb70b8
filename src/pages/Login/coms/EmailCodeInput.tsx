import React from 'react';
import { Form, Input, Button } from 'antd';
import { useTranslation } from 'react-i18next';
import type { FormInstance } from 'antd/es/form';
import type { LoginFormData } from '@/types/login';
import FormActions from './FormActions';
import SendCodeInput from '@/components/SendCodeInput';

interface EmailCodeInputProps {
  form: FormInstance<LoginFormData>;
  onSubmit: (values: Pick<LoginFormData, 'emailCode'>) => void;
  onSwitchToPassword: () => void;
  userEmail: string; // 添加用户邮箱显示
}

const EmailCodeInput: React.FC<EmailCodeInputProps> = ({
  form,
  onSubmit,
  onSwitchToPassword,
  userEmail,
}) => {
  const { t } = useTranslation();
  const handleSubmit = () => {
    if (form && onSubmit) {
      form
        .validateFields(['emailCode'])
        .then(values => {
          onSubmit(values);
        })
        .catch(errorInfo => {
          console.log('Email code validation failed:', errorInfo);
        });
    }
  };

  // 重新发送验证码
  const handleResendCode = () => {
    console.log('重新发送验证码到邮箱:', userEmail);
    // 这里可以调用发送验证码API，使用formData.email
  };

  return (
    <>
      <Form.Item
        label={
          <span className="leading-10">
            {t('auth.login.form.emailCodeLabel')}
          </span>
        }
        name="emailCode"
        rules={[{ required: true, message: t('common.form.required') }]}
        validateTrigger={['onChange', 'onBlur']}
        className="mb-6"
      >
        {/* <SendCodeInput /> */}
        <Input
          placeholder={t('auth.login.form.emailCodeLabel')}
          size="large"
          className="placeholder:font-inter h-[54px] rounded-md border-none bg-[#c9c9c9] px-5 text-black placeholder:(text-[12px] text-black/25)"
        />
      </Form.Item>

      {/* <div className="h-10 text-center leading-7">
        <Button color="primary" variant="link" onClick={handleResendCode}>
          {t('auth.login.form.resend')}
        </Button>
      </div> */}
      <FormActions
        buttonText={t('auth.login.form.submit')}
        onSubmit={handleSubmit}
      />
      <div className="mt-6 text-center">
        <span
          className="cursor-pointer text-[12px] text-[#ff5e13] hover:underline"
          onClick={onSwitchToPassword}
        >
          {t('auth.login.form.loginWithPassword')}
        </span>
      </div>
    </>
  );
};

export default EmailCodeInput;
