import React, { useState } from 'react';
import { Form, message, ConfigProvider } from 'antd';
import { useLanguage } from '@/hooks/useLanguage';

import UserNameInput from './coms/UserNameInput';
import EmailCodeInput from './coms/EmailCodeInput';
import PasswordInput from './coms/PasswordInput';
import type { LoginFormData } from '@/types/login';
import SelectLanguage from '@/components/SelectLanguage';
import { useAuthStore } from '@/store/authStore';
import { useNavigate, useLocation } from 'react-router-dom';

import logoIcon from '@/assets/logo.svg';

type LoginStep = 'userName' | 'auth';
type AuthMethod = 'email' | 'password';

const Login: React.FC = () => {
  const { t } = useLanguage();
  const navigate = useNavigate();
  const location = useLocation();

  const [currentStep, setCurrentStep] = useState<LoginStep>('userName');
  const [authMethod, setAuthMethod] = useState<AuthMethod>('password');
  const [form] = Form.useForm<LoginFormData>();

  // 使用useState管理完整的表单数据
  const [formData, setFormData] = useState<LoginFormData>({
    email: '<EMAIL>',
    password: 'password123',
    emailCode: '',
  });

  // 用户名提交
  const handleUsernameSubmit = (values: { email: string }) => {
    // 更新formData中的email字段
    const updatedFormData = { ...formData, email: values.email };
    setFormData(updatedFormData);

    setCurrentStep('auth');
  };

  // 密码登录提交
  const handlePasswordSubmit = (values: Pick<LoginFormData, 'password'>) => {
    // 更新formData中的password字段
    const updatedFormData = { ...formData, password: values.password };
    setFormData(updatedFormData);

    // 这里可以调用登录API，使用updatedFormData
    handleLogin(updatedFormData);
  };

  // 邮箱验证码登录提交
  const handleEmailCodeSubmit = (values: Pick<LoginFormData, 'emailCode'>) => {
    // 更新formData中的emailCode字段
    const updatedFormData = { ...formData, emailCode: values.emailCode };
    setFormData(updatedFormData);

    // 调登录API，使用updatedFormData
    handleLogin(updatedFormData);
  };

  const { login } = useAuthStore();
  // 统一的登录处理函数
  const handleLogin = async (loginData: LoginFormData) => {
    console.log('loginData----', loginData);

    try {
      // 调用实际的登录API
      await login(loginData);
      // TODO 跳转的逻辑在RedirectIfAuthenticated中处理
      // const url = location.state?.from?.pathname || '/';
      // navigate(url, { replace: true });
      // console.log('登录成功跳转到', url);

      message.success('登录成功');
    } catch (error) {
      console.error('登录失败:', error);
      message.error('登录失败，请重试');
    }
  };

  // 切换到邮箱验证码登录
  const handleSwitchToEmailCode = () => {
    setAuthMethod('email');
  };

  // 切换到密码登录
  const handleSwitchToPassword = () => {
    setAuthMethod('password');
  };

  const renderStepContent = () => {
    switch (currentStep) {
      case 'userName':
        return (
          <UserNameInput
            form={form}
            onSubmit={handleUsernameSubmit}
            initialEmail={formData.email} // 传递初始值以支持返回编辑
          />
        );
      case 'auth':
        return authMethod === 'email' ? (
          <EmailCodeInput
            form={form}
            onSubmit={handleEmailCodeSubmit}
            onSwitchToPassword={handleSwitchToPassword}
            userEmail={formData.email}
          />
        ) : (
          <PasswordInput
            form={form}
            onSubmit={handlePasswordSubmit}
            onSwitchToEmailCode={handleSwitchToEmailCode}
            initialPassword={formData.password}
          />
        );
      default:
        return null;
    }
  };

  return (
    <ConfigProvider
      theme={{
        components: {
          Form: {
            labelFontSize: 12,
          },
        },
      }}
    >
      <div className="relative min-h-screen flex items-center justify-center bg-black text-[#656565]">
        <div className="absolute inset-0 bg-[#0d0d0d]" />

        <div className="relative z-10 h-520px flex flex-col items-center">
          <div className="mb-8">
            <img
              src={logoIcon}
              alt="Yuequ Logo"
              className="h-[60px] w-[69px]"
            />
          </div>

          <h1 className="font-arial mb-16 text-center text-[32px] text-[#ff5e13] font-bold">
            {t('auth.login.title')}
          </h1>

          <div className="w-[496px]">
            <Form
              form={form}
              layout="vertical"
              onValuesChange={(changedValues, allValues) => {
                // console.log('Form values changed:', changedValues, allValues);
              }}
            >
              {renderStepContent()}
            </Form>
          </div>
        </div>
      </div>
    </ConfigProvider>
  );
};

export default Login;
