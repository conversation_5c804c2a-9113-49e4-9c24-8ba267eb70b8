import React, { useState } from 'react';
import { Input, Button } from 'antd';
import { useLanguage } from '@/hooks/useLanguage';

const SendCodeInput = () => {
  const [countdown, setCountdown] = useState(0);
  const { t } = useLanguage();
  const handleResendCode = () => {
    setCountdown(60);
    setTimeout(() => {
      setCountdown(0);
    }, 60000);
  };
  return (
    <Input
      placeholder={t('auth.login.form.emailCodeLabel')}
      size="large"
      className="placeholder:font-inter h-[54px] rounded-md border-none bg-[#c9c9c9] px-5 text-black placeholder:(text-[12px] text-black/25)"
      suffix={
        <div>
          <span>{countdown}</span>
          <Button
            variant="link"
            onClick={handleResendCode}
            className="ml-2 text-[12px] text-[#ff5e13] hover:underline"
          >
            {t('auth.login.form.resend')}
          </Button>
        </div>
      }
    />
  );
};

export default SendCodeInput;
